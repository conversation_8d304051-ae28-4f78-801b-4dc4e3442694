#include "stm32f10x.h"
#include "sys.h"
#include "usart.h"
#include "timer.h"
#include "delay.h"
#include "rtc.h"
#include "led.h"
#include "clock_display.h"
#include "clock_styles.h"
#include "key.h"
#include "uart_protocol.h"
#include <string.h>

// 样式切换变量
static uint32_t g_StyleSwitchTimer = 0;

int main(void)
{
    uint8_t key;
    static uint8_t style_demo_mode = 1;  // 演示模式标志

    // 系统初始化
    SystemInit();

    // LED初始化
    LED_Init();

    // 按键初始化
    KEY_Init();

    // 串口初始化
    uart1_init(115200);

    // 实时时钟初始化
    RTC_Init();

    // 定时器初始化
    TIM2_Init(999, 71);  // 1ms定时器 (72MHz / (71+1) / (999+1) = 1000Hz)
    TIM3_Init(499, 7199); // 500ms定时器 (72MHz / (7199+1) / (499+1) = 2Hz)
    TIM4_Init();  // 初始化TIM4用于delay_us和delay_ms函数

    // 串口通信协议初始化
    UART_Protocol_Init();

    // 初始化样式系统（默认现代简约风格）
    Clock_InitStyle();

    // 显示初始化
    Clock_Init();

    // �ֶ���������б�
    memset(&g_AlarmManager, 0, sizeof(g_AlarmManager));
    
    // ���Ӷ��������������֤LED��˸�ʹ��ڷ��͹���
    Alarm alarm;

    // ����1: 30��󴥷�
    alarm.hour = 0;
    alarm.minute = 0;
    alarm.second = 30;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 30s");
    Alarm_Add(&g_AlarmManager, &alarm);

    // ����2: 1���Ӻ󴥷�
    alarm.hour = 0;
    alarm.minute = 1;
    alarm.second = 0;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 1min");
    Alarm_Add(&g_AlarmManager, &alarm);

    // ����3: 1��30��󴥷�
    alarm.hour = 0;
    alarm.minute = 1;
    alarm.second = 30;
    alarm.enabled = 1;
    alarm.days = 0x7F; // ÿ��
    strcpy(alarm.name, "Test Alarm 1m30s");
    Alarm_Add(&g_AlarmManager, &alarm);
    
    // 打印系统初始化完成信息
    printf("\r\n=== Dual Style Clock System Demo ===\r\n");
    printf("Current time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second);

    // 显示当前闹钟信息
    printf("Alarm list (%d alarms):\r\n", g_AlarmManager.count);
    for(uint8_t i=0; i<g_AlarmManager.count; i++) {
        Alarm* a = &g_AlarmManager.alarms[i];
        printf("[%d] %02d:%02d:%02d %s Status:%s Repeat:0x%02X\r\n",
               i, a->hour, a->minute, a->second,
               a->name, a->enabled ? "ON" : "OFF", a->days);
    }

    printf("\r\nAvailable Styles:\r\n");
    printf("1. Modern Minimalist (Default)\r\n");
    printf("   - Clean white background\r\n");
    printf("   - Large digital display\r\n");
    printf("   - Card-based UI\r\n");
    printf("   - Blue accents\r\n");
    printf("2. Classic Vintage\r\n");
    printf("   - Warm beige background\r\n");
    printf("   - Analog clock with Roman numerals\r\n");
    printf("   - Wood-style frames\r\n");
    printf("   - Gold accents\r\n");
    printf("\r\nDemo Mode: Styles will auto-switch every 30 seconds\r\n");
    printf("Press any key to disable auto-switch\r\n");
    printf("=====================================\r\n");
    
    while(1)
    {
        // 按键检测处理
        key = KEY_Scan(0);
        if(key != KEY_NONE) {
            // 禁用演示模式
            if (style_demo_mode) {
                style_demo_mode = 0;
                printf("Demo mode disabled. Manual control enabled.\r\n");
            }

            // 根据按键类型显示操作信息
            switch(key) {
                case KEY1_PRESSED:
                    printf("KEY1按下: 确认操作\r\n");
                    break;
                case KEY2_PRESSED:
                    printf("KEY2按下: 上一项/切换样式\r\n");
                    // 手动切换样式
                    Clock_SetStyle(Clock_GetStyle() == STYLE_MODERN ? STYLE_CLASSIC : STYLE_MODERN);
                    break;
                case KEY3_PRESSED:
                    printf("KEY3按下: 下一项\r\n");
                    break;
                default:
                    printf("未知按键: %d\r\n", key);
                    break;
            }
            Clock_ProcessKey(key);
        }

        // 演示模式：自动切换样式
        if (style_demo_mode) {
            if (g_TimerCounter - g_StyleSwitchTimer > 30000) {  // 30秒切换一次
                uint8_t new_style = Clock_GetStyle() == STYLE_MODERN ? STYLE_CLASSIC : STYLE_MODERN;
                Clock_SetStyle(new_style);
                g_StyleSwitchTimer = g_TimerCounter;
                printf("Auto-switched to %s style\r\n",
                       new_style == STYLE_MODERN ? "Modern" : "Classic");
            }
        }

        // 显示时钟
        Clock_Display(&g_DateTime);

        // 处理串口通信协议
        UART_Protocol_Process();

        // 处理自动发送时间
        UART_AutoTime_Process();

        // 延时10ms，减少CPU占用
        delay_ms(10);
    }
}
