#include "rtc.h"
#include "usart.h"
#include <string.h>

// 全局时间变量
DateTime g_DateTime;
AlarmManager g_AlarmManager;
volatile uint32_t g_TimerCounter = 0; // 全局计时器计数

// 每个月的天数（非闰年）
const uint8_t g_MonthDays[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

// 初始化RTC
void RTC_Init(void)
{
    // 默认时间设置为2024年12月17日 星期二 14:30:00
    g_DateTime.year = 2024;
    g_DateTime.month = 12;
    g_DateTime.day = 17;
    g_DateTime.week = 2; // 星期二
    g_DateTime.hour = 14;
    g_DateTime.minute = 30;
    g_DateTime.second = 0;
    g_DateTime.millisecond = 0;

    // 初始化闹钟管理器
    Alarm_Init(&g_AlarmManager);

    printf("RTC initialized: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second);
}

// 设置时间
void RTC_SetTime(DateTime* time)
{
    g_DateTime = *time;
}

// 获取时间
void RTC_GetTime(DateTime* time)
{
    *time = g_DateTime;
}

// 判断是否为闰年
uint8_t IsLeapYear(uint16_t year)
{
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))
        return 1;
    else
        return 0;
}

// 更新日期时间
void DateTime_Update(DateTime* time)
{
    // 更新毫秒
    time->millisecond++;
    if (time->millisecond >= 1000)
    {
        time->millisecond = 0;

        // 更新秒
        time->second++;
        if (time->second >= 60)
        {
            time->second = 0;

            // 更新分钟
            time->minute++;
            if (time->minute >= 60)
            {
                time->minute = 0;

                // 更新小时
                time->hour++;
                if (time->hour >= 24)
                {
                    time->hour = 0;

                    // 更新星期
                    time->week++;
                    if (time->week >= 7)
                        time->week = 0;

                    // 更新日期
                    time->day++;
                    uint8_t monthDays = g_MonthDays[time->month - 1];

                    // 处理闰年2月
                    if (time->month == 2 && IsLeapYear(time->year))
                        monthDays = 29;

                    if (time->day > monthDays)
                    {
                        time->day = 1;

                        // 更新月份
                        time->month++;
                        if (time->month > 12)
                        {
                            time->month = 1;

                            // 更新年份
                            time->year++;
                        }
                    }
                }
            }
        }
    }
}

// 初始化闹钟管理器
void Alarm_Init(AlarmManager* manager)
{
    memset(manager, 0, sizeof(AlarmManager));
}

// 添加闹钟
uint8_t Alarm_Add(AlarmManager* manager, Alarm* alarm)
{
    if (manager->count >= 10)
        return 0; // 闹钟已满

    manager->alarms[manager->count] = *alarm;
    manager->count++;
    return 1;
}

// 删除闹钟
uint8_t Alarm_Delete(AlarmManager* manager, uint8_t index)
{
    if (index >= manager->count)
        return 0; // 索引无效

    // 移动后面的闹钟
    for (uint8_t i = index; i < manager->count - 1; i++)
    {
        manager->alarms[i] = manager->alarms[i + 1];
    }

    manager->count--;
    return 1;
}

// 更新闹钟
uint8_t Alarm_Update(AlarmManager* manager, uint8_t index, Alarm* alarm)
{
    if (index >= manager->count)
        return 0; // 索引无效

    manager->alarms[index] = *alarm;
    return 1;
}

// 检查闹钟
uint8_t Alarm_Check(AlarmManager* manager, DateTime* time)
{
    uint8_t triggered = 0;

    // 只在每秒0毫秒时检查闹钟触发
    if (time->millisecond == 0) {
        for (uint8_t i = 0; i < manager->count; i++)
        {
            Alarm* alarm = &manager->alarms[i];

            // 计算星期位掩码
            uint8_t week_bit = (1 << time->week);

            // 检查时间是否匹配
            uint8_t time_match = (alarm->hour == time->hour &&
                                 alarm->minute == time->minute &&
                                 alarm->second == time->second);

            // 检查重复日是否匹配
            uint8_t day_match = (alarm->days & week_bit);

            if (alarm->enabled && time_match && day_match)
            {
                // 闹钟触发
                char buffer[100];
                sprintf(buffer, "ALARM TRIGGERED: %s, Time: %02d:%02d:%02d Weekday:%d\r\n",
                        alarm->name, alarm->hour, alarm->minute, alarm->second, time->week);
                USART_SendString(USART1, buffer);
                triggered = 1;

                // 触发LED闪烁
                LED_StartBlink();
            }
        }
    }

    return triggered;
}